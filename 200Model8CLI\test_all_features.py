#!/usr/bin/env python3
"""
Comprehensive test script for 200Model8CLI

Tests all features and tools to ensure everything works correctly.
"""

import asyncio
import sys
import tempfile
from pathlib import Path

# Add src to path for development
sys.path.insert(0, str(Path(__file__).parent / "src"))

from model8cli.core.config import Config
from model8cli.core.api import OpenRouterClient
from model8cli.core.models import ModelManager
from model8cli.core.session import SessionManager
from model8cli.tools.base import ToolRegistry
from model8cli.tools.file_ops import FileOperations
from model8cli.tools.web_tools import WebTools
from model8cli.tools.git_tools import GitTools
from model8cli.tools.system_tools import SystemTools
from model8cli.tools.code_tools import CodeTools


async def test_file_operations(tool_registry: ToolRegistry, temp_dir: Path):
    """Test file operations"""
    print("\n🗂️  Testing File Operations...")
    
    test_file = temp_dir / "test.py"
    test_content = '''def hello_world():
    """A simple hello world function."""
    print("Hello from 200Model8CLI!")
    return "success"

if __name__ == "__main__":
    hello_world()
'''
    
    # Test write file
    result = await tool_registry.execute_tool(
        "write_file",
        path=str(test_file),
        content=test_content,
        create_backup=False
    )
    assert result.success, f"Write file failed: {result.error}"
    print("  ✅ Write file: PASSED")
    
    # Test read file
    result = await tool_registry.execute_tool("read_file", path=str(test_file))
    assert result.success, f"Read file failed: {result.error}"
    assert result.result["content"] == test_content
    print("  ✅ Read file: PASSED")
    
    # Test search files
    result = await tool_registry.execute_tool(
        "search_files",
        directory=str(temp_dir),
        content="hello_world",
        recursive=False
    )
    assert result.success, f"Search files failed: {result.error}"
    assert len(result.result["results"]) > 0
    print("  ✅ Search files: PASSED")
    
    # Test create directory
    new_dir = temp_dir / "subdir"
    result = await tool_registry.execute_tool("create_directory", path=str(new_dir))
    assert result.success, f"Create directory failed: {result.error}"
    assert new_dir.exists()
    print("  ✅ Create directory: PASSED")
    
    print("  🎉 All file operations tests PASSED!")


async def test_web_tools(tool_registry: ToolRegistry):
    """Test web tools"""
    print("\n🌐 Testing Web Tools...")
    
    # Test web search
    result = await tool_registry.execute_tool(
        "web_search",
        query="Python programming tutorial",
        max_results=3
    )
    if result.success:
        print("  ✅ Web search: PASSED")
    else:
        print(f"  ⚠️  Web search: SKIPPED ({result.error})")
    
    # Test web fetch
    result = await tool_registry.execute_tool(
        "web_fetch",
        url="https://httpbin.org/json",
        extract_text=True
    )
    if result.success:
        print("  ✅ Web fetch: PASSED")
    else:
        print(f"  ⚠️  Web fetch: SKIPPED ({result.error})")
    
    # Test extract code
    result = await tool_registry.execute_tool(
        "extract_code",
        url="https://raw.githubusercontent.com/python/cpython/main/README.rst",
        raw_content=True
    )
    if result.success:
        print("  ✅ Extract code: PASSED")
    else:
        print(f"  ⚠️  Extract code: SKIPPED ({result.error})")
    
    print("  🎉 Web tools tests completed!")


async def test_code_tools(tool_registry: ToolRegistry):
    """Test code analysis tools"""
    print("\n🔍 Testing Code Tools...")
    
    test_code = '''
def fibonacci(n):
    """Calculate fibonacci number."""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

def main():
    for i in range(10):
        print(f"fib({i}) = {fibonacci(i)}")

if __name__ == "__main__":
    main()
'''
    
    # Test analyze code
    result = await tool_registry.execute_tool(
        "analyze_code",
        code=test_code,
        language="python",
        analysis_type="detailed"
    )
    assert result.success, f"Analyze code failed: {result.error}"
    assert result.result["analysis"]["syntax_valid"]
    assert len(result.result["analysis"]["functions"]) >= 2
    print("  ✅ Analyze code: PASSED")
    
    # Test check syntax
    result = await tool_registry.execute_tool(
        "check_syntax",
        code=test_code,
        language="python"
    )
    assert result.success, f"Check syntax failed: {result.error}"
    assert result.result["valid"]
    print("  ✅ Check syntax: PASSED")
    
    # Test format code
    result = await tool_registry.execute_tool(
        "format_code",
        code="def test():print('hello')",
        language="python"
    )
    assert result.success, f"Format code failed: {result.error}"
    print("  ✅ Format code: PASSED")
    
    print("  🎉 All code tools tests PASSED!")


async def test_system_tools(tool_registry: ToolRegistry):
    """Test system tools"""
    print("\n💻 Testing System Tools...")
    
    # Test system info
    result = await tool_registry.execute_tool("system_info")
    assert result.success, f"System info failed: {result.error}"
    assert "platform" in result.result
    print("  ✅ System info: PASSED")
    
    # Test check dependencies
    result = await tool_registry.execute_tool(
        "check_dependencies",
        dependencies=["python", "pip"],
        check_type="command"
    )
    assert result.success, f"Check dependencies failed: {result.error}"
    print("  ✅ Check dependencies: PASSED")
    
    # Test environment
    result = await tool_registry.execute_tool(
        "environment",
        action="get",
        variable_name="PATH"
    )
    assert result.success, f"Environment failed: {result.error}"
    print("  ✅ Environment: PASSED")
    
    # Test execute command (safe command)
    result = await tool_registry.execute_tool(
        "execute_command",
        command="echo Hello World",
        timeout=5
    )
    if result.success:
        print("  ✅ Execute command: PASSED")
    else:
        print(f"  ⚠️  Execute command: SKIPPED ({result.error})")
    
    print("  🎉 System tools tests completed!")


async def test_git_tools(tool_registry: ToolRegistry, temp_dir: Path):
    """Test Git tools"""
    print("\n📦 Testing Git Tools...")
    
    # Initialize a git repo for testing
    import subprocess
    try:
        subprocess.run(["git", "init"], cwd=temp_dir, check=True, capture_output=True)
        subprocess.run(["git", "config", "user.name", "Test User"], cwd=temp_dir, check=True)
        subprocess.run(["git", "config", "user.email", "<EMAIL>"], cwd=temp_dir, check=True)
        
        # Create a test file
        test_file = temp_dir / "README.md"
        test_file.write_text("# Test Repository\nThis is a test.")
        
        subprocess.run(["git", "add", "README.md"], cwd=temp_dir, check=True)
        subprocess.run(["git", "commit", "-m", "Initial commit"], cwd=temp_dir, check=True)
        
        # Test git status
        result = await tool_registry.execute_tool("git_status", path=str(temp_dir))
        assert result.success, f"Git status failed: {result.error}"
        print("  ✅ Git status: PASSED")
        
        # Test git branch
        result = await tool_registry.execute_tool(
            "git_branch",
            action="list",
            path=str(temp_dir)
        )
        assert result.success, f"Git branch failed: {result.error}"
        print("  ✅ Git branch: PASSED")
        
        print("  🎉 Git tools tests PASSED!")
        
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("  ⚠️  Git tools: SKIPPED (Git not available)")


async def test_session_management(config: Config):
    """Test session management"""
    print("\n💬 Testing Session Management...")
    
    session_manager = SessionManager(config)
    
    # Create session
    session = session_manager.create_session(
        name="Test Session",
        description="Testing session management"
    )
    assert session is not None
    print("  ✅ Create session: PASSED")
    
    # Add messages
    session_manager.add_message("user", "Hello, how are you?")
    session_manager.add_message("assistant", "I'm doing well, thank you!")
    
    assert len(session.messages) == 2
    print("  ✅ Add messages: PASSED")
    
    # Get context messages
    context = session_manager.get_context_messages()
    assert len(context) == 2
    print("  ✅ Get context: PASSED")
    
    # List sessions
    sessions = session_manager.list_sessions()
    assert len(sessions) >= 1
    print("  ✅ List sessions: PASSED")
    
    print("  🎉 Session management tests PASSED!")


async def test_ai_integration(config: Config, tool_registry: ToolRegistry):
    """Test AI integration if API key is available"""
    print("\n🤖 Testing AI Integration...")
    
    if not config.openrouter_api_key or config.openrouter_api_key == "":
        print("  ⚠️  AI Integration: SKIPPED (No API key configured)")
        return
    
    try:
        async with OpenRouterClient(config) as api_client:
            # Test health check
            healthy = await api_client.health_check()
            if not healthy:
                print("  ⚠️  AI Integration: SKIPPED (API not healthy)")
                return
            
            print("  ✅ API health check: PASSED")
            
            # Test model manager
            model_manager = ModelManager(config, api_client)
            await model_manager.initialize()
            
            models = model_manager.get_available_models()
            assert len(models) > 0
            print("  ✅ Model manager: PASSED")
            
            # Test simple chat
            from model8cli.core.api import Message
            messages = [Message(role="user", content="Say 'Hello from 200Model8CLI!'")]
            
            response = await api_client.chat_completion(
                messages=messages,
                max_tokens=50
            )
            
            assert response.choices
            print("  ✅ Chat completion: PASSED")
            
            print("  🎉 AI integration tests PASSED!")
            
    except Exception as e:
        print(f"  ⚠️  AI Integration: FAILED ({str(e)})")


async def main():
    """Run all tests"""
    print("🚀 200Model8CLI - Comprehensive Feature Test")
    print("=" * 50)
    
    # Setup
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Load configuration
        config_path = temp_path / "config.yaml"
        config_data = {"api": {"openrouter_key": "test-key"}}
        import yaml
        with open(config_path, 'w') as f:
            yaml.dump(config_data, f)
        
        config = Config(config_path)
        config.config_dir = temp_path  # Override config dir
        
        # Initialize tool registry
        tool_registry = ToolRegistry(config)
        
        # Register all tools
        file_ops = FileOperations(config)
        for tool in file_ops.get_tools():
            tool_registry.register_tool(tool)
        
        web_tools = WebTools(config)
        for tool in web_tools.get_tools():
            tool_registry.register_tool(tool)
        
        git_tools = GitTools(config)
        for tool in git_tools.get_tools():
            tool_registry.register_tool(tool)
        
        system_tools = SystemTools(config)
        for tool in system_tools.get_tools():
            tool_registry.register_tool(tool)
        
        code_tools = CodeTools(config)
        for tool in code_tools.get_tools():
            tool_registry.register_tool(tool)
        
        print(f"✅ Initialized with {len(tool_registry.get_enabled_tools())} tools")
        
        # Run tests
        try:
            await test_file_operations(tool_registry, temp_path)
            await test_code_tools(tool_registry)
            await test_system_tools(tool_registry)
            await test_web_tools(tool_registry)
            await test_git_tools(tool_registry, temp_path)
            await test_session_management(config)
            await test_ai_integration(config, tool_registry)
            
            print("\n🎉 ALL TESTS COMPLETED!")
            print("\n📊 Test Summary:")
            print("  ✅ File Operations: PASSED")
            print("  ✅ Code Tools: PASSED") 
            print("  ✅ System Tools: PASSED")
            print("  ✅ Session Management: PASSED")
            print("  ⚠️  Web Tools: PARTIAL (network dependent)")
            print("  ⚠️  Git Tools: PARTIAL (git dependency)")
            print("  ⚠️  AI Integration: DEPENDS ON API KEY")
            
            print("\n🚀 200Model8CLI is ready to use!")
            print("\nNext steps:")
            print("1. Set your OpenRouter API key: export OPENROUTER_API_KEY='your-key'")
            print("2. Run: python install.py")
            print("3. Start using: 200model8cli")
            
        except Exception as e:
            print(f"\n❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
            return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
