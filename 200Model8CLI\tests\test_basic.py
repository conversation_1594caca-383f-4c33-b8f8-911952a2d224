"""
Basic tests for 200Model8CLI

Tests core functionality without requiring API keys.
"""

import pytest
import asyncio
import tempfile
from pathlib import Path
import sys

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from model8cli.core.config import Config
from model8cli.core.session import SessionManager
from model8cli.tools.base import ToolRegistry
from model8cli.tools.file_ops import FileOperations
from model8cli.utils.helpers import get_file_info, format_file_size


class TestConfig:
    """Test configuration management"""
    
    def test_config_creation(self):
        """Test config can be created"""
        # Use temporary config path to avoid affecting user config
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            
            # This should work even without API key for basic testing
            try:
                config = Config(config_path)
                assert config is not None
                assert config.default_model is not None
                assert config.api_timeout > 0
            except ValueError as e:
                # Expected if no API key is set
                assert "OpenRouter API key is required" in str(e)
    
    def test_config_defaults(self):
        """Test configuration defaults"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            
            # Create minimal config for testing
            config_data = {
                "api": {"openrouter_key": "test-key"},
                "models": {"default": "test-model"},
            }
            
            import yaml
            with open(config_path, 'w') as f:
                yaml.dump(config_data, f)
            
            config = Config(config_path)
            
            assert config.api.openrouter_key == "test-key"
            assert config.models.default == "test-model"
            assert config.api.timeout == 30  # Default value
            assert config.tools.file_operations_enabled is True


class TestSessionManager:
    """Test session management"""
    
    def test_session_creation(self):
        """Test session creation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            
            # Create minimal config
            config_data = {"api": {"openrouter_key": "test-key"}}
            import yaml
            with open(config_path, 'w') as f:
                yaml.dump(config_data, f)
            
            config = Config(config_path)
            config.config_dir = Path(temp_dir)  # Override config dir
            
            session_manager = SessionManager(config)
            session = session_manager.create_session(
                name="Test Session",
                description="Test session"
            )
            
            assert session is not None
            assert session.metadata.name == "Test Session"
            assert session.metadata.description == "Test session"
            assert len(session.messages) == 0
    
    def test_session_messages(self):
        """Test adding messages to session"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            
            config_data = {"api": {"openrouter_key": "test-key"}}
            import yaml
            with open(config_path, 'w') as f:
                yaml.dump(config_data, f)
            
            config = Config(config_path)
            config.config_dir = Path(temp_dir)
            
            session_manager = SessionManager(config)
            session = session_manager.create_session(name="Test")
            
            # Add messages
            msg1 = session_manager.add_message("user", "Hello")
            msg2 = session_manager.add_message("assistant", "Hi there!")
            
            assert len(session.messages) == 2
            assert session.messages[0].role == "user"
            assert session.messages[0].content == "Hello"
            assert session.messages[1].role == "assistant"
            assert session.messages[1].content == "Hi there!"


class TestFileOperations:
    """Test file operations tools"""
    
    @pytest.fixture
    def setup_tools(self):
        """Setup tools for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            
            config_data = {"api": {"openrouter_key": "test-key"}}
            import yaml
            with open(config_path, 'w') as f:
                yaml.dump(config_data, f)
            
            config = Config(config_path)
            
            tool_registry = ToolRegistry(config)
            file_ops = FileOperations(config)
            
            for tool in file_ops.get_tools():
                tool_registry.register_tool(tool)
            
            yield tool_registry, Path(temp_dir)
    
    @pytest.mark.asyncio
    async def test_write_and_read_file(self, setup_tools):
        """Test writing and reading files"""
        tool_registry, temp_dir = setup_tools
        
        test_file = temp_dir / "test.txt"
        test_content = "Hello, 200Model8CLI!"
        
        # Write file
        result = await tool_registry.execute_tool(
            "write_file",
            path=str(test_file),
            content=test_content,
            create_backup=False
        )
        
        assert result.success is True
        assert test_file.exists()
        
        # Read file
        result = await tool_registry.execute_tool(
            "read_file",
            path=str(test_file)
        )
        
        assert result.success is True
        assert result.result["content"] == test_content
        assert result.result["line_count"] == 1
    
    @pytest.mark.asyncio
    async def test_search_files(self, setup_tools):
        """Test file search functionality"""
        tool_registry, temp_dir = setup_tools
        
        # Create test files
        test_files = [
            ("file1.py", "def hello():\n    print('Hello from Python!')"),
            ("file2.js", "function hello() {\n    console.log('Hello from JS!');\n}"),
            ("readme.md", "# Test Project\nThis is a test project for 200Model8CLI."),
        ]
        
        for filename, content in test_files:
            file_path = temp_dir / filename
            with open(file_path, 'w') as f:
                f.write(content)
        
        # Search by pattern
        result = await tool_registry.execute_tool(
            "search_files",
            directory=str(temp_dir),
            pattern="*.py",
            recursive=False
        )
        
        assert result.success is True
        results = result.result["results"]
        assert len(results) >= 1
        assert any("file1.py" in r["path"] for r in results)
        
        # Search by content
        result = await tool_registry.execute_tool(
            "search_files",
            directory=str(temp_dir),
            content="Hello",
            recursive=False
        )
        
        assert result.success is True
        results = result.result["results"]
        assert len(results) >= 2  # Should find in both Python and JS files
    
    @pytest.mark.asyncio
    async def test_create_directory(self, setup_tools):
        """Test directory creation"""
        tool_registry, temp_dir = setup_tools
        
        new_dir = temp_dir / "subdir" / "nested"
        
        result = await tool_registry.execute_tool(
            "create_directory",
            path=str(new_dir)
        )
        
        assert result.success is True
        assert new_dir.exists()
        assert new_dir.is_dir()


class TestUtilities:
    """Test utility functions"""
    
    def test_file_info(self):
        """Test file info utility"""
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write("Test content")
            temp_path = f.name
        
        try:
            info = get_file_info(temp_path)
            
            assert info["exists"] is True
            assert info["is_file"] is True
            assert info["size"] > 0
            assert "path" in info
            assert "modified" in info
        finally:
            Path(temp_path).unlink()
    
    def test_format_file_size(self):
        """Test file size formatting"""
        assert format_file_size(0) == "0 B"
        assert format_file_size(1024) == "1.0 KB"
        assert format_file_size(1024 * 1024) == "1.0 MB"
        assert format_file_size(1024 * 1024 * 1024) == "1.0 GB"


class TestToolRegistry:
    """Test tool registry functionality"""
    
    def test_tool_registration(self):
        """Test tool registration and retrieval"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config_path = Path(temp_dir) / "config.yaml"
            
            config_data = {"api": {"openrouter_key": "test-key"}}
            import yaml
            with open(config_path, 'w') as f:
                yaml.dump(config_data, f)
            
            config = Config(config_path)
            
            tool_registry = ToolRegistry(config)
            file_ops = FileOperations(config)
            
            # Register tools
            for tool in file_ops.get_tools():
                tool_registry.register_tool(tool)
            
            # Test retrieval
            tools = tool_registry.get_all_tools()
            assert len(tools) > 0
            
            # Test specific tool
            read_tool = tool_registry.get_tool("read_file")
            assert read_tool is not None
            assert read_tool.name == "read_file"
            
            # Test tool definitions
            definitions = tool_registry.get_tool_definitions()
            assert len(definitions) > 0
            assert all("function" in defn for defn in definitions)


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
