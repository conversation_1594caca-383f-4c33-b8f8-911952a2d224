# 200Model8CLI - OpenRouter CLI Agent

A sophisticated command-line interface tool that mimics <PERSON>'s functionality but uses OpenRouter's API to access multiple AI models with comprehensive tool calling capabilities.

## Features

### 🤖 Multi-Model Support
- **Claude 3 Opus/Sonnet** - Anthropic's most capable models
- **GPT-4/GPT-3.5** - OpenAI's flagship models  
- **Llama 3** - Meta's open-source powerhouse
- **Gemini Pro** - Google's advanced AI model
- **Dynamic model switching** during conversations

### 🛠️ Comprehensive Tool System
- **File Operations** - Read, write, edit, search, backup, diff
- **Web Integration** - Search, fetch, extract code from URLs
- **Git & GitHub** - Smart commits, PR creation, branch management
- **Code Analysis** - Syntax checking, formatting, testing
- **System Operations** - Safe command execution, dependency checking

### 💻 Rich CLI Experience
- **Interactive Mode** - Persistent conversations with context
- **Streaming Responses** - Real-time AI interaction
- **Syntax Highlighting** - Beautiful code display
- **Rich Formatting** - Markdown rendering in terminal
- **Progress Indicators** - Visual feedback for long operations

### 🔒 Security & Safety
- **Input Validation** - Sanitized inputs and safe execution
- **Automatic Backups** - File protection before modifications
- **Confirmation Prompts** - User consent for destructive operations
- **Sandboxed Execution** - Safe code running environment

## Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/200Model8CLI.git
cd 200Model8CLI

# Install dependencies
pip install -r requirements.txt

# Install the CLI tool
pip install -e .

# Set up your OpenRouter API key
export OPENROUTER_API_KEY="your-api-key-here"

# Initialize configuration
200model8cli config init
```

### Basic Usage

```bash
# Interactive mode with default model
200model8cli

# Use specific model
200model8cli --model claude-3-opus

# File operations
200model8cli edit ./src/main.py "Add error handling to the login function"
200model8cli create ./tests/test_auth.py "Unit tests for authentication module"

# Git operations
200model8cli git commit "Added user authentication system"
200model8cli github pr "Create PR for user authentication feature"

# Web search and fetch
200model8cli search "Python async best practices"
200model8cli fetch https://github.com/user/repo/blob/main/example.py
```

## Configuration

The CLI uses a YAML configuration file located at `~/.200model8cli/config.yaml`:

```yaml
api:
  openrouter_key: ${OPENROUTER_API_KEY}
  base_url: https://openrouter.ai/api/v1
  timeout: 30
  max_retries: 3

models:
  default: claude-3-sonnet
  available:
    - claude-3-opus
    - claude-3-sonnet
    - gpt-4
    - gpt-3.5

tools:
  web_search:
    enabled: true
    max_results: 5
  file_operations:
    enabled: true
    max_file_size: 10MB

ui:
  streaming: true
  syntax_highlighting: true
  rich_formatting: true
```

## Advanced Features

### Smart File Editing
```bash
# Edit multiple related files
200model8cli edit-related "Add logging to all API endpoints" ./src/routes/

# Refactor across files
200model8cli refactor "Rename User class to Account" ./src/

# Create project structure
200model8cli scaffold "Python FastAPI project with JWT auth and PostgreSQL"
```

### Git Workflow Integration
```bash
# AI-generated commit messages
200model8cli git auto-commit

# Smart branch management
200model8cli git workflow "standard feature branch workflow"

# Deploy via GitHub Actions
200model8cli deploy "Deploy to production"
```

### Session Management
```bash
# Save current session
200model8cli session save "feature-development"

# Load previous session
200model8cli session load "feature-development"

# List all sessions
200model8cli session list
```

## Development

### Project Structure
```
200Model8CLI/
├── src/model8cli/
│   ├── core/          # Core API and model management
│   ├── tools/         # Tool implementations
│   ├── ui/            # CLI interface and formatting
│   └── utils/         # Utilities and helpers
├── tests/             # Test suite
├── docs/              # Documentation
└── examples/          # Usage examples
```

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=ai_cli

# Run specific test file
pytest tests/test_api.py
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see [LICENSE](LICENSE) file for details.

## Support

- 📖 [Documentation](docs/)
- 🐛 [Issue Tracker](https://github.com/yourusername/200Model8CLI/issues)
- 💬 [Discussions](https://github.com/yourusername/200Model8CLI/discussions)
