"""
Ollama API client for local model support
"""
import asyncio
import json
from typing import Dict, List, Optional, AsyncGenerator, Any
import httpx
from dataclasses import dataclass

from model8cli.core.types import ChatMessage, ChatResponse
from model8cli.utils.logging import get_logger

logger = get_logger(__name__)


@dataclass
class OllamaModel:
    """Ollama model information"""
    name: str
    size: int
    digest: str
    modified_at: str


class OllamaClient:
    """Client for Ollama API"""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url.rstrip('/')
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def list_models(self) -> List[OllamaModel]:
        """List available Ollama models"""
        try:
            response = await self.client.get(f"{self.base_url}/api/tags")
            response.raise_for_status()
            
            data = response.json()
            models = []
            
            for model_data in data.get("models", []):
                models.append(OllamaModel(
                    name=model_data["name"],
                    size=model_data["size"],
                    digest=model_data["digest"],
                    modified_at=model_data["modified_at"]
                ))
            
            return models
            
        except Exception as e:
            logger.error("Failed to list Ollama models", error=str(e))
            return []
    
    async def is_available(self) -> bool:
        """Check if Ollama is running"""
        try:
            response = await self.client.get(f"{self.base_url}/api/tags", timeout=5.0)
            return response.status_code == 200
        except:
            return False
    
    async def chat_completion(
        self,
        model: str,
        messages: List[ChatMessage],
        stream: bool = False,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> ChatResponse:
        """Create chat completion with Ollama"""
        try:
            # Convert messages to Ollama format
            ollama_messages = []
            for msg in messages:
                ollama_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
            
            request_data = {
                "model": model,
                "messages": ollama_messages,
                "stream": stream,
                "options": {
                    "temperature": temperature,
                }
            }
            
            if max_tokens:
                request_data["options"]["num_predict"] = max_tokens
            
            response = await self.client.post(
                f"{self.base_url}/api/chat",
                json=request_data
            )
            response.raise_for_status()
            
            if stream:
                return self._handle_streaming_response(response)
            else:
                data = response.json()
                return ChatResponse(
                    id=f"ollama-{model}",
                    model=model,
                    choices=[{
                        "message": {
                            "role": "assistant",
                            "content": data["message"]["content"]
                        }
                    }],
                    usage={
                        "prompt_tokens": data.get("prompt_eval_count", 0),
                        "completion_tokens": data.get("eval_count", 0),
                        "total_tokens": data.get("prompt_eval_count", 0) + data.get("eval_count", 0)
                    },
                    created=0
                )
                
        except Exception as e:
            logger.error("Ollama chat completion failed", error=str(e))
            raise
    
    async def _handle_streaming_response(self, response) -> AsyncGenerator[str, None]:
        """Handle streaming response from Ollama"""
        async for line in response.aiter_lines():
            if line:
                try:
                    data = json.loads(line)
                    if "message" in data and "content" in data["message"]:
                        yield data["message"]["content"]
                except json.JSONDecodeError:
                    continue
    
    async def pull_model(self, model_name: str) -> bool:
        """Pull a model from Ollama registry"""
        try:
            response = await self.client.post(
                f"{self.base_url}/api/pull",
                json={"name": model_name},
                timeout=300.0  # 5 minutes for model download
            )
            response.raise_for_status()
            return True
            
        except Exception as e:
            logger.error("Failed to pull Ollama model", model=model_name, error=str(e))
            return False
    
    async def delete_model(self, model_name: str) -> bool:
        """Delete a model from Ollama"""
        try:
            response = await self.client.delete(
                f"{self.base_url}/api/delete",
                json={"name": model_name}
            )
            response.raise_for_status()
            return True
            
        except Exception as e:
            logger.error("Failed to delete Ollama model", model=model_name, error=str(e))
            return False
