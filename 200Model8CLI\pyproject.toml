[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "200Model8CLI"
version = "1.0.0"
description = "A sophisticated CLI tool that uses OpenRouter's API to access multiple AI models with tool calling capabilities"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "200Model8CLI Development Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Tools",
    "Topic :: Utilities",
]
requires-python = ">=3.8"
dependencies = [
    "click>=8.0.0",
    "httpx>=0.24.0",
    "rich>=13.0.0",
    "pyyaml>=6.0",
    "aiofiles>=23.0.0",
    "python-dotenv>=1.0.0",
    "tiktoken>=0.4.0",
    "pygments>=2.14.0",
    "asyncio-throttle>=1.0.2",
    "pathspec>=0.11.0",
    "watchdog>=3.0.0",
    "GitPython>=3.1.30",
    "beautifulsoup4>=4.12.0",
    "requests>=2.31.0",
    "urllib3>=2.0.0",
    "cryptography>=41.0.0",
    "keyring>=24.0.0",
    "configparser>=5.3.0",
    "toml>=0.10.2",
    "prompt-toolkit>=3.0.0",
    "colorama>=0.4.6",
    "tabulate>=0.9.0",
    "jsonschema>=4.17.0",
    "marshmallow>=3.19.0",
    "structlog>=23.1.0",
    "loguru>=0.7.0",
    "cachetools>=5.3.0",
    "diskcache>=5.6.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]
docs = [
    "sphinx>=6.0.0",
    "sphinx-rtd-theme>=1.2.0",
    "myst-parser>=1.0.0",
]

[project.scripts]
"200model8cli" = "model8cli.cli:main"
"m8cli" = "model8cli.cli:main"

[project.urls]
Homepage = "https://github.com/yourusername/200Model8CLI"
Documentation = "https://200model8cli.readthedocs.io/"
Repository = "https://github.com/yourusername/200Model8CLI.git"
"Bug Tracker" = "https://github.com/yourusername/200Model8CLI/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
model8cli = ["templates/*.yaml", "templates/*.json", "config/*.yaml"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = ["*/tests/*", "*/test_*.py"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
